"use client";

import { useEffect, useState } from "react";
import { toast } from "sonner";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { ofetch } from "ofetch";
import { AuthError, IgnoreError, Credits402Error, handleError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { Download, LoaderCircle, CoinsIcon, SparklesIcon, PlusIcon, Check, ChevronDownIcon, CrownIcon } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { FLUX_1_KONTEXT_DEV, FLUX_1_KONTEXT_MAX, FLUX_1_KONTEXT_PRO, HIDREAM_E1_1, ImageModel } from "@/lib/utils-image-model";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { downloadImageFromBase64, downloadImageFromBase64WithWatermark, imageUrlToBase64 } from "@/lib/file/utils-file";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { IMAGE_SIZE_LIMIT_ } from "@/lib/constants";
import { useDropzone } from "react-dropzone";
import { Button, buttonVariants } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { uploadFile } from "@/lib/file/upload-file";
import { sendGTMEvent } from "@next/third-parties/google";
import { EVENT_EDIT_IMAGE } from "@/lib/track-events";

const imageModels: ImageModel[] = [FLUX_1_KONTEXT_DEV, FLUX_1_KONTEXT_PRO, FLUX_1_KONTEXT_MAX, HIDREAM_E1_1];

export default function ImageEditor() {
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	useEffect(() => {
		if (user && !userHasPaid) {
			setPlanBoxOpen(true);
		}
	}, []);

	const [model, setModel] = useState<ImageModel>(FLUX_1_KONTEXT_DEV);
	const [prompt, setPrompt] = useState<string>("");
	const [image, setImage] = useState<string | null>(null);

	const [allImages, setAllImages] = useState<string[]>([
		"https://static.dreampik.art/source/202508/20250809-019890d3e34872d98d56e67bba358217.jpg",
		"https://fast3d.io/images/blog/optimized/post-1-lg.webp",
	]);
	const {
		getRootProps,
		getInputProps,
		open: openDropzone,
	} = useDropzone({
		noClick: true,
		multiple: false,
		accept: {
			"image/jpeg": [],
			"image/png": [],
			"image/webp": [],
		},
		maxFiles: 1,
		onError: console.error,
		onDrop: (acceptedFiles, fileRejections, event) => {
			if (fileRejections.length > 0) {
				const message = fileRejections.at(0)?.errors.at(0)?.message;
				console.error?.(new Error(message));
				return;
			}

			handleLocalFileDrop(acceptedFiles);
		},
	});
	const [uploadingImage, setUploadingImage] = useState<boolean>(false);
	const handleLocalFileDrop = async (files: File[]) => {
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		if (!files || files.length === 0) return;
		let file = files[0];

		if (file.size > IMAGE_SIZE_LIMIT_) {
			toast.warning("Image exceeds 4MB. Please upload a smaller one.");
			return;
		}

		try {
			setUploadingImage(true);
			const { file_url } = await uploadFile(file);
			// const file_url = await fileToBase64(file);

			setImage(file_url);
			setAllImages([file_url, ...allImages]);
		} catch (error: any) {
			console.error("Failed to upload image:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error(`Upload image failed: ${error.message}`);
		} finally {
			setUploadingImage(false);
		}
	};

	const [submitting, setSubmitting] = useState(false);
	const handleGenerateImage = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		const promtpTrim = prompt.trim();
		if (!promtpTrim) return;

		sendGTMEvent({
			event: EVENT_EDIT_IMAGE,
			membership_level: user?.membershipLevel,
			model: model.id,
		});

		try {
			setSubmitting(true);
			const { status, message, resultUrl } = await ofetch("/api/v1/image/edit-image", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: {
					model: model.id,
					prompt: promtpTrim,
					image: image,
				},
			});
			handleError(status, message);
			refreshUser();

			if (resultUrl) {
				setImage(resultUrl);
				setAllImages([resultUrl, ...allImages]);
			}

			toast.success("Generate success.");
		} catch (error: any) {
			console.error("Failed to generate image:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error(error.message || "Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const [downloading, setDownloading] = useState<boolean>(false);

	return (
		<div className="flex h-[calc(100vh-56px)] w-full flex-col">
			<div className="flex h-full w-full flex-col gap-4 px-4 pt-4 pb-4 md:flex-row" {...getRootProps()}>
				<div className="flex flex-1 flex-col items-center justify-between gap-4">
					<div className="group relative flex min-h-0 w-full flex-1 items-center justify-center">
						{image ? (
							<div className="bg-muted relative flex aspect-square h-full max-h-[calc(100vh-326px)] max-w-full rounded-lg md:max-h-[calc(100vh-242px)]">
								{/* <img src={`${OSS_URL_HOST}mkt/pages/ai-image-generator/demo.webp`} alt="AI Image Generator Demo" className="object-contain" /> */}
								<div
									className="aspect-square max-w-full bg-contain bg-center bg-no-repeat"
									// style={{ backgroundImage: `url(${OSS_URL_HOST}mkt/pages/ai-image-generator/demo.webp)` }}
									style={{ backgroundImage: `url(${image})` }}
									role="img"
								></div>

								<div className="absolute right-2 bottom-2 z-10 items-center gap-1">
									<DropdownMenu>
										<DropdownMenuTrigger className={cn(buttonVariants({ variant: "default", size: "icon" }))}>
											{downloading ? <LoaderCircle className="h-4 w-4 animate-spin" /> : <Download />}
										</DropdownMenuTrigger>
										<DropdownMenuContent className="border" align="start">
											{!userHasPaid && (
												<DropdownMenuItem
													className="flex cursor-pointer flex-row items-center gap-2 text-[13px] font-[350] [&>svg]:size-[14px]"
													onClick={async () => {
														try {
															setDownloading(true);
															const base64 = await imageUrlToBase64(image, { noCache: true });
															await downloadImageFromBase64WithWatermark(base64);
														} catch (error) {
															console.error("Failed to download image:", error);
														} finally {
															setDownloading(false);
														}
													}}
												>
													<Download className="text-zinc-800" /> Download with watermark
												</DropdownMenuItem>
											)}
											<DropdownMenuItem
												className="flex cursor-pointer flex-row items-center gap-2 text-[13px] font-[350] [&>svg]:size-[14px]"
												onClick={async () => {
													if (!userHasPaid) {
														setPlanBoxOpen(true);
														return;
													}
													try {
														setDownloading(true);
														const base64 = await imageUrlToBase64(image, { noCache: true });
														await downloadImageFromBase64(base64);
													} catch (error) {
														console.error("Failed to download image:", error);
													} finally {
														setDownloading(false);
													}
												}}
											>
												{userHasPaid ? (
													<>
														<Download className="text-zinc-800" /> Download
													</>
												) : (
													<>
														<CrownIcon className="text-yellow-500" /> Download without watermark
													</>
												)}
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								</div>
							</div>
						) : (
							<div className="">
								<Button
									disabled={uploadingImage}
									className="relative h-auto w-full flex-row overflow-hidden rounded-2xl p-8"
									onClick={openDropzone}
								>
									{uploadingImage ? <LoaderCircle className="h-4 w-4 animate-spin" /> : <PlusIcon />}
									Upload Image
								</Button>
							</div>
						)}
					</div>

					<div className="bg-muted flex w-full max-w-3xl shrink-0 flex-col gap-1 rounded-lg px-3 py-3">
						<Textarea
							placeholder="What do you want to change?"
							rows={5}
							maxLength={2000}
							className={cn(
								"h-16 p-1",
								"resize-none border-none shadow-none focus-visible:shadow-none focus-visible:ring-0",
								"[&::-webkit-scrollbar-thumb]:bg-muted-foreground [&::-webkit-scrollbar]:h-1 [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-thumb]:rounded-full",
							)}
							value={prompt}
							onChange={(e) => setPrompt(e.target.value)}
						/>

						<div className="flex flex-row items-center justify-between gap-1 px-1">
							<DropdownMenu>
								<DropdownMenuTrigger
									className={cn(
										"ring-offset-background flex h-9 flex-row items-center justify-between gap-1 rounded-md border px-3 py-2 text-sm whitespace-nowrap shadow-none ring-offset-0",
										"focus:ring-ring data-placeholder:text-muted-foreground focus:ring-0 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
									)}
								>
									<div className="flex flex-row items-center gap-2">
										<img className="size-[14px]" src={model.logo} alt="" />
										<span className="text-[13px] font-[350]">{model.name}</span>
									</div>
									<ChevronDownIcon className="-me-1 opacity-60" size={16} aria-hidden="true" />
								</DropdownMenuTrigger>
								<DropdownMenuContent className="min-w-(--radix-dropdown-menu-trigger-width)" align="start">
									{imageModels.map((modelOption, index) => (
										<DropdownMenuItem
											key={index}
											className="flex cursor-pointer flex-row items-center justify-between gap-2 text-[13px] font-[350] [&>svg]:size-[14px]"
											onClick={() => {
												setModel(modelOption);
											}}
										>
											<div className="flex flex-row items-center gap-2">
												<img className="size-[14px] text-white" src={modelOption.logo} alt="" />
												<span className="">{modelOption.name}</span>
												<span className="00 flex min-w-[32px] flex-row items-center justify-between gap-0.5 rounded border px-[4px] text-[10px]">
													<CoinsIcon className="size-[10px]" /> {modelOption.credits}
												</span>
											</div>
											{modelOption.id === model.id && <Check className="size-[14px]" />}
										</DropdownMenuItem>
									))}
								</DropdownMenuContent>
							</DropdownMenu>
							<SubmitButton
								className="bg-brand-success hover:bg-brand-success/80"
								isSubmitting={submitting}
								onClick={handleGenerateImage}
								{...{ disabled: submitting || !prompt.trim() || !image }}
							>
								<SparklesIcon />
								Generate
							</SubmitButton>
						</div>
					</div>
				</div>

				<div className="bg-muted flex shrink-0 flex-row gap-1 rounded-lg p-2 md:flex-col">
					<div className="">
						<Button variant="secondary" className="h-[56px] w-[56px] flex-row overflow-hidden rounded-md bg-white" onClick={openDropzone}>
							<PlusIcon />
							<input {...getInputProps()} />
						</Button>
					</div>
					<div
						className={cn(
							"flex flex-row gap-1 overflow-auto md:flex-col",
							"[&::-webkit-scrollbar-thumb]:bg-muted-foreground [&::-webkit-scrollbar]:h-1 md:[&::-webkit-scrollbar]:h-0 md:[&::-webkit-scrollbar]:w-0 [&::-webkit-scrollbar-thumb]:rounded-full",
						)}
					>
						{allImages.map((imageOption, index) => (
							<div key={index} className={cn("relative aspect-square h-[56px] w-[56px] rounded-md", image === imageOption && "p-0.5")}>
								{/* <img
								src={image}
								alt="AI Image Generator Demo"
								className="aspect-square h-full w-full rounded object-cover"
								onContextMenu={(e) => e.preventDefault()}
								onDragStart={(e) => e.preventDefault()}
							/> */}
								<div
									className={cn(
										"aspect-square cursor-pointer rounded-md border bg-cover bg-center bg-no-repeat bg-origin-content",
										image === imageOption && "ring-2 ring-blue-500",
									)}
									style={{ backgroundImage: `url(${imageOption})` }}
									role="img"
									onClick={() => setImage(imageOption)}
								></div>
							</div>
						))}
					</div>
				</div>
			</div>
		</div>
	);
}
