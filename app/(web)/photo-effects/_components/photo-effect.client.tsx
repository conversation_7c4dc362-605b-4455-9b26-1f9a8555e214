"use client";

import { useEffect, useState } from "react";
import { toast } from "sonner";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { ofetch } from "ofetch";
import { AuthError, IgnoreError, Credits402Error, handleError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { Download, CoinsIcon, ChevronRightIcon, PlusCircleIcon, SparklesIcon, LoaderIcon, XIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button, buttonVariants } from "@/components/ui/button";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { downloadImageFromBase64, downloadImageFromBase64WithWatermark, fileToBase<PERSON>, imageUrlToBase64 } from "@/lib/file/utils-file";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { Hint } from "@/components/ui/custom/hint";
import { uploadFile } from "@/lib/file/upload-file";
import { allPhotoEffects, PhotoStyleID, type PhotoEffectType } from "@/lib/utils-photo-effects";
import { WEBNAME } from "@/lib/constants";
import { useRouter } from "nextjs-toploader/app";
import { usePathname, useSearchParams } from "next/navigation";
import { sendGTMEvent } from "@next/third-parties/google";
import { EVENT_GEN_PHOTO_EFFECT } from "@/lib/track-events";
import { useDropzone } from "react-dropzone";

export default function PhotoEffectClient({ styleId = PhotoStyleID.Ghibli }: { styleId?: string }) {
	const searchParams = useSearchParams();
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();
	const router = useRouter();
	const pathname = usePathname();

	const [showDemo, setShowDemo] = useState(true);
	const [image, setImage] = useState<{
		base64: string;
		url: string;
	} | null>(null);
	const [previewImageBase64, setPreviewImageBase64] = useState<string | null>(null);
	// const [previewImageBase64, setPreviewImageBase64] = useState<string | null>(
	// 	"https://static.dreampik.art/dev/media/202507/202507060197dfe4348775aa8e2365f85b22e613.png",
	// );

	// Style selection states
	const [selectedStyle, setSelectedStyle] = useState<PhotoEffectType>(allPhotoEffects.find((style) => style.id === styleId) ?? allPhotoEffects[0]);
	const [isStyleDialogOpen, setIsStyleDialogOpen] = useState(false);

	useEffect(() => {
		if (pathname.includes("restyler")) {
			const paramStyleId = searchParams.get("styleId");
			if (paramStyleId) {
				const style = allPhotoEffects.find((s) => s.id === paramStyleId);
				setSelectedStyle(style ?? allPhotoEffects[0]);
			}
		}
	}, [pathname]);

	const [uploadingImage, setUploadingImage] = useState<boolean>(false);

	const {
		getRootProps,
		getInputProps,
		open: openDropzone,
	} = useDropzone({
		noClick: true,
		multiple: false,
		accept: {
			"image/jpeg": [],
			"image/png": [],
			"image/webp": [],
		},
		maxFiles: 1,
		onError: console.error,
		onDrop: async (acceptedFiles, fileRejections, event) => {
			if (!session) {
				setSignInBoxOpen(true);
				return;
			}
			if (fileRejections.length > 0) {
				const message = fileRejections.at(0)?.errors.at(0)?.message;
				console.error?.(new Error(message));
				return;
			}

			const file = acceptedFiles[0];
			if (!file) return;

			try {
				setUploadingImage(true);
				const { file_url } = await uploadFile(file);
				// const file_url = "";
				const base64 = await fileToBase64(file);
				setImage({
					url: file_url,
					base64,
				});
			} catch (error: any) {
				console.error("Failed to upload image:", error.message);
				if (error instanceof AuthError) {
					setSignInBoxOpen(true);
					return;
				}
				toast.error(`Upload image failed: ${error.message}`);
			} finally {
				setUploadingImage(false);
			}
		},
	});

	const [submitting, setSubmitting] = useState(false);
	const handleGenerateImage = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		setShowDemo(false);

		sendGTMEvent({
			event: EVENT_GEN_PHOTO_EFFECT,
			membership_level: user?.membershipLevel,
			photo_effect_style: selectedStyle.id,
		});

		try {
			setPreviewImageBase64(null);
			setSubmitting(true);
			const { status, message, resultUrl } = await ofetch(selectedStyle.apiEndpoint, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: {
					image: image?.url,
					style: selectedStyle.id,
				},
			});
			handleError(status, message);
			refreshUser();

			if (resultUrl) {
				const base64 = await imageUrlToBase64(resultUrl as string, { noCache: true });
				setPreviewImageBase64(base64);
			}

			toast.success("Generate success.");
		} catch (error: any) {
			setPreviewImageBase64(null);
			console.error("Failed to generate image:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error(error.message ?? "Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const [downloadingImage, setDownloadingImage] = useState<boolean>(false);

	return (
		<div className="container flex w-full flex-col gap-4 overflow-hidden px-6 md:flex-row">
			<div className="bg-muted flex w-full flex-col rounded-xl">
				{/* <AppTitleAndBackward title="Photo Effect" backUrl="/photo-effects" /> */}

				<div className="space-y-4 p-4 md:p-6">
					{/* Style Selection */}
					<div className="space-y-[6px]">
						{/* <p className="text-secondary-foregroun text-sm">Style</p> */}
						<Button
							size="lg"
							variant="outline"
							className="bg-input hover:bg-input/80 h-12 w-full justify-between gap-2 rounded-lg py-1.5 text-[13px] font-medium has-[>svg]:px-1.5"
							onClick={() => setIsStyleDialogOpen(true)}
						>
							<div className="flex h-full flex-row items-center gap-1">
								{selectedStyle.demoImage && (
									<img
										src={selectedStyle.demoImage}
										alt={`${selectedStyle.pageName} style image`}
										className="aspect-video h-full rounded-[8px] border object-cover"
										loading="lazy"
									/>
								)}
								<span className="ml-1">{selectedStyle ? selectedStyle.pageName : "Select Style"}</span>
							</div>
							<ChevronRightIcon className="opacity-60" size={16} aria-hidden="true" />
						</Button>
					</div>

					<div className="bg-input overflow-hidden rounded-lg" {...getRootProps()}>
						{image ? (
							<div className="group relative h-[260px] w-full">
								<img
									src={image.base64}
									alt="Model"
									className="h-full w-full object-cover"
									onContextMenu={(e) => e.preventDefault()}
									onDragStart={(e) => e.preventDefault()}
								/>
								<button
									onClick={() => {
										setImage(null);
										setPreviewImageBase64(null);
									}}
									className="bg-foreground/90 absolute top-2 right-2 h-9 w-9 cursor-pointer rounded-full p-2 text-white"
								>
									<XIcon className="size-4" />
								</button>
							</div>
						) : (
							<div
								onClick={() => {
									if (uploadingImage) return;
									openDropzone();
								}}
								className="flex h-[260px] cursor-pointer items-center justify-center"
							>
								{uploadingImage ? (
									<div className="text-muted-foreground flex flex-col items-center">
										<LoaderIcon className="animate-spin" />
										<p className="text-sm">Uploading image</p>
									</div>
								) : (
									<div className={cn(buttonVariants({ size: "lg" }), "bg-action hover:bg-action/80 h-14 rounded-xl")}>
										<PlusCircleIcon />
										<p>Upload image</p>
									</div>
								)}
								<input {...getInputProps()} />
							</div>
						)}
					</div>

					<SubmitButton
						size="lg"
						className="bg-foreground hover:bg-foreground/80 w-full rounded-lg"
						isSubmitting={submitting}
						onClick={handleGenerateImage}
						{...{ disabled: submitting || !image }}
					>
						<SparklesIcon />
						Generate
						{session && (
							<p className="text-secondary flex flex-row items-center gap-0.5 px-2 py-0.5 text-[11px]">
								(<CoinsIcon className="size-3" />
								12)
							</p>
						)}
					</SubmitButton>
				</div>
			</div>

			<div className="bg-muted flex w-full items-start justify-center overflow-y-hidden rounded-xl p-4">
				<div className="h-full w-full">
					{showDemo && selectedStyle.sampleImage ? (
						<div className="mx-auto flex h-full max-w-2xl flex-col justify-center space-y-2">
							<div className="bg-muted mx-auto flex aspect-[3/2] w-full max-w-3xl overflow-hidden rounded-lg">
								<img
									src={selectedStyle.sampleImage}
									alt={`${selectedStyle.pageName} sample image`}
									className="h-full w-full rounded object-contain"
								/>
							</div>
						</div>
					) : (
						<div className="mx-auto flex h-full max-w-3xl flex-col justify-center space-y-4">
							<div className={cn("group relative mx-auto flex aspect-[3/2] w-full max-w-3xl", (previewImageBase64 || submitting) && "")}>
								{previewImageBase64 && (
									<>
										<div className="relative">
											<div
												className="aspect-[3/2] h-full w-full rounded-lg bg-contain bg-center bg-no-repeat"
												style={{ backgroundImage: `url(${previewImageBase64})` }}
												role="img"
												aria-label="Upscaled image"
											></div>
											{!userHasPaid && (
												<p className="font-heading absolute bottom-1/2 left-1/2 z-50 -translate-x-1/2 translate-y-1/2 text-3xl text-white/70">
													{WEBNAME}.art
												</p>
											)}
										</div>
										<div className="absolute right-2 bottom-2 z-10 items-center gap-1">
											{previewImageBase64 && (
												<Hint label="Download image" sideOffset={10}>
													<div className="relative">
														<SubmitButton
															isSubmitting={downloadingImage}
															disabled={!previewImageBase64}
															size="icon"
															onClick={async () => {
																try {
																	setDownloadingImage(true);
																	if (userHasPaid) {
																		await downloadImageFromBase64(previewImageBase64);
																	} else {
																		await downloadImageFromBase64WithWatermark(previewImageBase64);
																	}
																} catch (error) {
																	console.error("Failed to download image:", error);
																} finally {
																	setDownloadingImage(false);
																}
															}}
															className="bg-foreground hover:bg-foreground/80"
														>
															<Download />
														</SubmitButton>
													</div>
												</Hint>
											)}
										</div>
									</>
								)}
							</div>
						</div>
					)}
				</div>
			</div>

			{/* Style Selection Dialog */}
			<Dialog open={isStyleDialogOpen} onOpenChange={setIsStyleDialogOpen}>
				<DialogContent className="max-h-[80vh] overflow-y-auto sm:max-w-5xl">
					<DialogHeader>
						<DialogTitle className="font-medium">Select a photo effect style</DialogTitle>
					</DialogHeader>
					<div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
						{allPhotoEffects.map((style) => (
							<div
								// href={style.pageUrl}
								key={style.id}
								className={cn(
									"bg-muted hover:bg-muted-foreground/20 flex cursor-pointer flex-col rounded-lg transition-all",
									selectedStyle?.id === style.id ? "bg-muted-foreground/20 border-action border" : "",
								)}
								onClick={() => {
									setSelectedStyle(style);
									setIsStyleDialogOpen(false);
									if (!style.notPage) {
										router.push(style.pageUrl);
									}
								}}
							>
								<div className="overflow-hidden rounded-t-md">
									{style.demoImage && (
										<img
											src={style.demoImage}
											alt={style.name}
											className="aspect-video h-full w-full object-cover"
											onError={(e) => {
												// Hide image if it fails to load
												e.currentTarget.style.display = "none";
											}}
										/>
									)}
								</div>
								<div className="p-2">
									<h3 className="text-sm">{style.pageName}</h3>
								</div>
							</div>
						))}
					</div>
				</DialogContent>
			</Dialog>
		</div>
	);
}
