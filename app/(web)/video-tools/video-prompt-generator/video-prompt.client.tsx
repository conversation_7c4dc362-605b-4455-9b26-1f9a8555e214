"use client";

import { useState } from "react";
import { toast } from "sonner";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { ofetch } from "ofetch";
import { AuthError, IgnoreError, Credits402Error, handleError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import {
	LoaderCircle,
	Loader2,
	ImagePlus,
	Trash,
	CoinsIcon,
	Check,
	ChevronDownIcon,
	TerminalIcon,
	ClipboardIcon,
	CircleCheckIcon,
	CheckIcon,
} from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { useSubmitTimer } from "@/hooks/use-submit-timer";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { fileToBase64 } from "@/lib/file/utils-file";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { Dropzone, DropzoneEmptyState } from "@/components/ui/kibo-ui/dropzone/index";
import { uploadFile } from "@/lib/file/upload-file";
import { sendGTMEvent } from "@next/third-parties/google";
import { EVENT_GEN_IMAGE_TO_IMAGE, EVENT_GEN_TEXT_TO_IMAGE } from "@/lib/track-events";
import { IMAGE_SIZE_LIMIT_ } from "@/lib/constants";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { useCopyToClipboard } from "usehooks-ts";

const styles = ["Minimalist", "Simple", "Detailed", "Descriptive", "Dynamic", "Cinematic", "Documentary", "Animation", "Action", "Experimental"];
const cameraStyles = [
	"None",
	"Steadicam flow",
	"Drone aerials",
	"Handheld urgency",
	"Crane elegance",
	"Dolly precision",
	"VR 360",
	"Multi-angle rig",
	"Static tripod",
	"Gimbal smoothness",
	"Slider motion",
	"Jib sweep",
	"POV immersion",
	"Time-slice array",
	"Macro extreme",
	"Tilt-shift miniature",
	"Snorricam character",
	"Whip pan dynamics",
	"Dutch angle tension",
	"Underwater housing",
	"Periscope lens",
];
const cameraDirections = [
	"None",
	"Zoom in",
	"Zoom out",
	"Pan left",
	"Pan right",
	"Tilt up",
	"Tilt down",
	"Orbital rotation",
	"Push in",
	"Pull out",
	"Track forward",
	"Track backward",
	"Spiral in",
	"Spiral out",
	"Arc movement",
	"Diagonal traverse",
	"Vertical rise",
	"Vertical descent",
];
const pacings = [
	"None",
	"Slow burn",
	"Rhythmic pulse",
	"Frantic energy",
	"Ebb and flow",
	"Hypnotic drift",
	"Time-lapse rush",
	"Stop-motion staccato",
	"Gradual build",
	"Quick cut rhythm",
	"Long take meditation",
	"Jump cut energy",
	"Match cut flow",
	"Cross-dissolve dreamscape",
	"Parallel action",
	"Slow motion impact",
	"Ramping dynamics",
	"Montage tempo",
	"Continuous flow",
	"Episodic breaks",
];
const specialEffects = [
	"None",
	"Practical effects",
	"CGI enhancement",
	"Analog glitches",
	"Light painting",
	"Projection mapping",
	"Nanosecond exposures",
	"Double exposure",
	"Smoke diffusion",
	"Lens flare artistry",
	"Particle systems",
	"Holographic overlay",
	"Chromatic aberration",
	"Digital distortion",
	"Wire removal",
	"Motion capture",
	"Miniature integration",
	"Weather simulation",
	"Color grading",
	"Mixed media composite",
	"Neural style transfer",
];
const promtLengths = ["Short", "Medium", "Long"];

export default function VideoPrompt({ imageToImage }: { imageToImage?: boolean }) {
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	const [copiedText, copy] = useCopyToClipboard();
	const onCopy = (text: string) => () => {
		copy(text)
			.then(() => {
				// toast.success("Prompt copied! Click generate to create a new image")
				toast.success("Prompt copied!");
			})
			.catch((error: any) => {
				toast.error("Failed to copy!", error);
			});
	};
	const [showDemo, setShowDemo] = useState(true);

	const [prompt, setPrompt] = useState<string>("");
	const [style, setStyle] = useState<string>("Simple");
	const [cameraStyle, setCameraStyle] = useState<string>(cameraStyles[0]);
	const [cameraDirection, setCameraDirection] = useState<string>(cameraDirections[0]);
	const [pacing, setPacing] = useState<string>(pacings[0]);
	const [specialEffect, setSpecialEffect] = useState<string>(specialEffects[0]);
	const [promtLength, setPromtLength] = useState<string>(promtLengths[1]);
	const [image, setImage] = useState<{
		base64: string;
		url: string;
	} | null>(null);
	const [uploadingImage, setUploadingImage] = useState<boolean>(false);
	const handleLocalFileDrop = async (files: File[]) => {
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		if (!files || files.length === 0) return;
		let file = files[0];

		if (file.size > IMAGE_SIZE_LIMIT_) {
			toast.warning("Image exceeds 4MB. Please upload a smaller one.");
			return;
		}

		try {
			setUploadingImage(true);
			const { file_url } = await uploadFile(file);
			// const file_url = "";
			const base64 = await fileToBase64(file);
			setImage({
				url: file_url,
				base64,
			});
		} catch (error: any) {
			console.error("Failed to upload image:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error(`Upload image failed: ${error.message}`);
		} finally {
			setUploadingImage(false);
		}
	};

	const [result, setResult] = useState<string>("");

	const [submitting, setSubmitting] = useState(false);
	const { seconds } = useSubmitTimer(submitting);
	const handleGenerateImage = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		const promtpTrim = prompt.trim();
		if (!promtpTrim) return;

		setShowDemo(false);

		// sendGTMEvent({
		// 	event: image?.url ? EVENT_GEN_IMAGE_TO_IMAGE : EVENT_GEN_TEXT_TO_IMAGE,
		// 	membership_level: user?.membershipLevel,
		// 	model: model.id,
		// });

		try {
			setResult("");
			setSubmitting(true);

			const { status, message, result } = await ofetch("/api/v1/video/tool/prompt-generator", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: {
					prompt: promtpTrim,
					style,
					cameraStyle,
					cameraDirection,
					pacing,
					specialEffect,
					promtLength,
					image: image?.url,
				},
			});
			handleError(status, message);
			if (!userHasPaid) {
				refreshUser();
			}

			if (result) setResult(result);

			toast.success("Generate success.");
		} catch (error: any) {
			console.error("Failed to generate prompt:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error(error.message || "Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	return (
		<div className="container grid w-full grid-cols-1 gap-4 px-6 py-4 sm:grid-cols-2">
			{/* Options Section - Top */}
			<div className="bg-muted w-full rounded-xl p-6">
				<div className="grid grid-cols-2 gap-4">
					{/* Concept - spans full width */}
					<div className="col-span-2 space-y-[6px]">
						{/* <p className="text-secondary-foreground text-sm">Concept</p> */}
						<Textarea
							placeholder="Core concept or thematic input. e.g. A futuristic city at dusk."
							rows={5}
							maxLength={2000}
							className={cn(
								"max-h-24 min-h-24 resize-none border-none bg-white shadow-none",
								"[&::-webkit-scrollbar-thumb]:bg-muted-foreground [&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-thumb]:rounded-full",
							)}
							value={prompt}
							onChange={(e) => setPrompt(e.target.value)}
						/>
					</div>

					<div className="space-y-[6px]">
						{/* <p className="text-secondary-foreground text-sm">Style</p> */}
						<DropdownMenu>
							<DropdownMenuTrigger
								className={cn(
									"ring-offset-background flex h-9 w-full cursor-pointer flex-row items-center justify-between rounded-md bg-white px-3 py-2 text-sm whitespace-nowrap shadow-none ring-offset-0 hover:bg-white",
									"focus:ring-ring data-placeholder:text-muted-foreground focus:ring-0 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
								)}
							>
								<div className="flex flex-row items-center gap-2">
									<span className="">{style}</span>
								</div>
								<ChevronDownIcon className="opacity-50" size={16} aria-hidden="true" />
							</DropdownMenuTrigger>
							<DropdownMenuContent className="min-w-(--radix-dropdown-menu-trigger-width)">
								<p className="text-muted-foreground p-2 text-xs font-medium">Style</p>
								{styles.map((styleOption, index) => (
									<DropdownMenuItem
										key={index}
										className={cn(
											"flex cursor-pointer flex-row items-center justify-between gap-2 rounded-md py-2.5",
											styleOption === style && "bg-accent",
										)}
										onClick={() => {
											setStyle(styleOption);
										}}
									>
										<div className="flex flex-row items-center gap-2">
											<span className="">{styleOption}</span>
										</div>
										{/* {styleOption === style && <CircleCheckIcon className="size-4.5 text-black" />} */}
										{styleOption === style && (
											<p className="flex h-[18px] w-[18px] items-center justify-center rounded-full bg-black">
												<CheckIcon className="size-3 text-white" strokeWidth={3} />
											</p>
										)}
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
					</div>

					<div className="space-y-[6px]">
						{/* <p className="text-secondary-foreground text-xs">Camera Style</p> */}
						<DropdownMenu>
							<DropdownMenuTrigger
								className={cn(
									"ring-offset-background hover:bg-input bg-input flex h-9 w-full cursor-pointer flex-row items-center justify-between rounded-md border px-3 py-2 text-sm whitespace-nowrap shadow-none ring-offset-0",
									"focus:ring-ring data-placeholder:text-muted-foreground focus:ring-0 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
								)}
							>
								<div className="flex flex-row items-center gap-2">
									<span className="text-[13px] font-[350]">{cameraStyle === "None" }</span>
								</div>
								<ChevronDownIcon className="opacity-50" size={16} aria-hidden="true" />
							</DropdownMenuTrigger>
							<DropdownMenuContent className="min-w-(--radix-dropdown-menu-trigger-width)">
								{cameraStyles.map((styleOption, index) => (
									<DropdownMenuItem
										key={index}
										className="flex cursor-pointer flex-row items-center justify-between gap-2 text-[13px] font-[350] [&>svg]:size-[14px]"
										onClick={() => {
											setCameraStyle(styleOption);
										}}
									>
										<div className="flex flex-row items-center gap-2">
											<span className="">{styleOption}</span>
										</div>
										{styleOption === cameraStyle && <Check className="size-[14px]" />}
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
					</div>

					<div className="space-y-[6px]">
						{/* <p className="text-secondary-foreground text-xs">Camera Direction</p> */}
						<DropdownMenu>
							<DropdownMenuTrigger
								className={cn(
									"ring-offset-background hover:bg-input bg-input flex h-9 w-full cursor-pointer flex-row items-center justify-between rounded-md border px-3 py-2 text-sm whitespace-nowrap shadow-none ring-offset-0",
									"focus:ring-ring data-placeholder:text-muted-foreground focus:ring-0 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
								)}
							>
								<div className="flex flex-row items-center gap-2">
									<span className="text-[13px] font-[350]">{cameraDirection}</span>
								</div>
								<ChevronDownIcon className="opacity-50" size={16} aria-hidden="true" />
							</DropdownMenuTrigger>
							<DropdownMenuContent className="min-w-(--radix-dropdown-menu-trigger-width)">
								{cameraDirections.map((option, index) => (
									<DropdownMenuItem
										key={index}
										className="flex cursor-pointer flex-row items-center justify-between gap-2 text-[13px] font-[350] [&>svg]:size-[14px]"
										onClick={() => {
											setCameraDirection(option);
										}}
									>
										<div className="flex flex-row items-center gap-2">
											<span className="">{option}</span>
										</div>
										{option === cameraDirection && <Check className="size-[14px]" />}
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
					</div>

					<div className="space-y-[6px]">
						{/* <p className="text-secondary-foreground text-xs">Pacing</p> */}
						<DropdownMenu>
							<DropdownMenuTrigger
								className={cn(
									"ring-offset-background hover:bg-input bg-input flex h-9 w-full cursor-pointer flex-row items-center justify-between rounded-md border px-3 py-2 text-sm whitespace-nowrap shadow-none ring-offset-0",
									"focus:ring-ring data-placeholder:text-muted-foreground focus:ring-0 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
								)}
							>
								<div className="flex flex-row items-center gap-2">
									<span className="text-[13px] font-[350]">{pacing}</span>
								</div>
								<ChevronDownIcon className="opacity-50" size={16} aria-hidden="true" />
							</DropdownMenuTrigger>
							<DropdownMenuContent className="min-w-(--radix-dropdown-menu-trigger-width)">
								{pacings.map((option, index) => (
									<DropdownMenuItem
										key={index}
										className="flex cursor-pointer flex-row items-center justify-between gap-2 text-[13px] font-[350] [&>svg]:size-[14px]"
										onClick={() => {
											setPacing(option);
										}}
									>
										<div className="flex flex-row items-center gap-2">
											<span className="">{option}</span>
										</div>
										{option === pacing && <Check className="size-[14px]" />}
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
					</div>

					<div className="space-y-[6px]">
						{/* <p className="text-secondary-foreground text-xs">Special Effect</p> */}
						<DropdownMenu>
							<DropdownMenuTrigger
								className={cn(
									"ring-offset-background hover:bg-input bg-input flex h-9 w-full cursor-pointer flex-row items-center justify-between rounded-md border px-3 py-2 text-sm whitespace-nowrap shadow-none ring-offset-0",
									"focus:ring-ring data-placeholder:text-muted-foreground focus:ring-0 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
								)}
							>
								<div className="flex flex-row items-center gap-2">
									<span className="text-[13px] font-[350]">{specialEffect}</span>
								</div>
								<ChevronDownIcon className="opacity-50" size={16} aria-hidden="true" />
							</DropdownMenuTrigger>
							<DropdownMenuContent className="min-w-(--radix-dropdown-menu-trigger-width)">
								{specialEffects.map((option, index) => (
									<DropdownMenuItem
										key={index}
										className="flex cursor-pointer flex-row items-center justify-between gap-2 text-[13px] font-[350] [&>svg]:size-[14px]"
										onClick={() => {
											setSpecialEffect(option);
										}}
									>
										<div className="flex flex-row items-center gap-2">
											<span className="">{option}</span>
										</div>
										{option === specialEffect && <Check className="size-[14px]" />}
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
					</div>

					<div className="space-y-[6px]">
						{/* <p className="text-secondary-foreground text-xs">Prompt Length</p> */}
						<DropdownMenu>
							<DropdownMenuTrigger
								className={cn(
									"ring-offset-background hover:bg-input bg-input flex h-9 w-full cursor-pointer flex-row items-center justify-between rounded-md border px-3 py-2 text-sm whitespace-nowrap shadow-none ring-offset-0",
									"focus:ring-ring data-placeholder:text-muted-foreground focus:ring-0 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
								)}
							>
								<div className="flex flex-row items-center gap-2">
									<span className="text-[13px] font-[350]">{promtLength}</span>
								</div>
								<ChevronDownIcon className="opacity-50" size={16} aria-hidden="true" />
							</DropdownMenuTrigger>
							<DropdownMenuContent className="min-w-(--radix-dropdown-menu-trigger-width)">
								{promtLengths.map((option, index) => (
									<DropdownMenuItem
										key={index}
										className="flex cursor-pointer flex-row items-center justify-between gap-2 text-[13px] font-[350] [&>svg]:size-[14px]"
										onClick={() => {
											setPromtLength(option);
										}}
									>
										<div className="flex flex-row items-center gap-2">
											<span className="">{option}</span>
										</div>
										{option === promtLength && <Check className="size-[14px]" />}
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
					</div>

					<div className="col-span-2 space-y-[6px]">
						{/* <p className="text-secondary-foreground text-sm">Image(optional)</p> */}

						{image ? (
							<div className="group hover:bg-accent/50 relative h-[144px] w-full rounded border p-0">
								<img
									src={image.base64}
									alt="Model"
									className="h-[142px] w-full object-contain"
									onContextMenu={(e) => e.preventDefault()}
									onDragStart={(e) => e.preventDefault()}
								/>
								<div className="absolute inset-0 hidden items-center justify-center bg-black/50 group-hover:flex">
									<button
										onClick={() => setImage(null)}
										className="cursor-pointer rounded-full p-2 text-white/80 transition-colors hover:text-white"
									>
										<Trash className="size-4" />
									</button>
								</div>
							</div>
						) : (
							<div
								onClick={() => {
									if (!session) {
										setSignInBoxOpen(true);
									}
								}}
							>
								<Dropzone
									multiple={false}
									maxFiles={1}
									noClick={!session}
									onDragEnter={() => {
										if (!session) {
											setSignInBoxOpen(true);
											return;
										}
									}}
									onDrop={(files) => handleLocalFileDrop(files)}
									accept={{
										"image/jpeg": [".jpg", ".jpeg", ".png", ".webp"],
									}}
									onError={console.error}
									className="hover:bg-accent/50 h-[144px] w-full cursor-pointer border p-0"
								>
									<DropzoneEmptyState>
										<>
											{uploadingImage ? (
												<Loader2 className="text-muted-foreground animate-spin" />
											) : (
												<div className="text-muted-foreground hover:text-foreground flex h-10 flex-col items-center gap-2 font-normal">
													<ImagePlus />
													<p>Click to upload an image</p>
												</div>
											)}
										</>
									</DropzoneEmptyState>
								</Dropzone>
							</div>
						)}
					</div>
				</div>

				{/* Generate Button Section */}
				<div className="space-y-1 border-t p-3 lg:p-4">
					<p className="text-muted-foreground flex flex-row items-center gap-1 text-[11px] font-[350]">
						<CoinsIcon className="size-3" />
						{userHasPaid ? (
							<>0 credits</>
						) : (
							<>
								2 credits. 0 credits for{" "}
								<button className="text-primary underline underline-offset-4" onClick={() => setPlanBoxOpen(true)}>
									paid
								</button>{" "}
								users.
							</>
						)}
					</p>
					<SubmitButton
						variant="secondary"
						className="bg-primary hover:bg-primary/80 w-full cursor-pointer"
						isSubmitting={submitting}
						onClick={handleGenerateImage}
						{...{ disabled: submitting || !prompt.trim() }}
					>
						Generate
					</SubmitButton>
				</div>
			</div>

			{/* Result Section - Bottom */}
			<div className="bg-muted flex items-start justify-center rounded-xl p-6">
				<div className="h-full w-full">
					{showDemo ? (
						<div className="mx-auto flex h-full flex-col justify-center space-y-2">
							<p className="text-muted-foreground text-sm">Sample Prompt</p>
							<div className="bg-card mx-auto flex w-full flex-col gap-2 rounded-md border p-4 text-sm">
								<TerminalIcon className="text-muted-foreground size-4" />
								<p className="text-foreground">
									As dusk settles over the futuristic city, buildings of glass and steel gleam with soft, reflected light from the setting
									sun. The camera uses a serene slider motion to zoom in, capturing the city from a distance and slowly focusing on the
									intricate details of its architecture. The calming scene emphasizes the gentle transition from day to night, drawing viewers
									into a world where technology and tranquility coexist harmoniously.
								</p>
							</div>
						</div>
					) : (
						<div className="mx-auto flex h-full flex-col justify-center space-y-2">
							<p className="text-muted-foreground text-sm">Prompt</p>
							<div className="bg-card mx-auto flex min-h-[160px] w-full flex-col justify-between gap-2 rounded-md border p-4 text-sm">
								{submitting && (
									<p className="text-muted-foreground flex h-full w-full flex-col items-center justify-center text-center">
										<LoaderCircle className="size-6 animate-spin" />
										<span className="text-sm tabular-nums">{seconds}s</span>
									</p>
								)}
								{result && (
									<>
										<p className="text-foreground">{result}</p>
										<div className="flex w-full justify-end">
											<Button size="sm" variant="default" className="font-normal" onClick={onCopy(result)}>
												<ClipboardIcon /> Copy
											</Button>
										</div>
									</>
								)}
							</div>
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
