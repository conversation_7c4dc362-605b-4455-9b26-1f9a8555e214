"use client";

import { useState } from "react";
import { toast } from "sonner";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { ofetch } from "ofetch";
import { AuthError, IgnoreError, Credits402Error, handleError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { Download, LoaderCircle, Loader2, ImagePlus, Trash, Sparkles, CoinsIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { useSubmitTimer } from "@/hooks/use-submit-timer";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { downloadImageFromUrl, fileToBase64 } from "@/lib/file/utils-file";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { Hint } from "@/components/ui/custom/hint";
import { Dropzone, DropzoneEmptyState } from "@/components/ui/kibo-ui/dropzone/index";
import { uploadFile } from "@/lib/file/upload-file";
import { Comparison, ComparisonHandle, ComparisonItem } from "@/components/ui/kibo-ui/comparison";
import { IMAGE_SIZE_LIMIT_, OSS_URL_HOST } from "@/lib/constants";
import { sendGTMEvent } from "@next/third-parties/google";
// import { AppTitleAndBackward } from "../../app-backward";
import { EVENT_GEN_IMAGE_TOOL } from "@/lib/track-events";
import { ReactSVG } from "react-svg";

export function VectorizeImageClient() {
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	const [showDemo, setShowDemo] = useState(true);
	const [image, setImage] = useState<{
		base64: string;
		url: string;
	} | null>(null);
	const [previewImage, setPreviewImage] = useState<string | null>(null);

	const [uploadingImage, setUploadingImage] = useState<boolean>(false);
	const handleLocalFileDrop = async (files: File[]) => {
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		if (!files || files.length === 0) return;
		let file = files[0];

		if (file.size > IMAGE_SIZE_LIMIT_) {
			toast.warning("Image exceeds 4MB. Please upload a smaller one.");
			return;
		}
		try {
			setUploadingImage(true);
			const { file_url } = await uploadFile(file);
			// const file_url = "";
			const base64 = await fileToBase64(file);
			setImage({
				url: file_url,
				base64,
			});
		} catch (error: any) {
			console.error("Failed to upload image:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error(`Upload image failed: ${error.message}`);
		} finally {
			setUploadingImage(false);
		}
	};

	const [submitting, setSubmitting] = useState(false);
	const { seconds } = useSubmitTimer(submitting);
	const handleGenerateImage = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		setShowDemo(false);

		sendGTMEvent({
			event: EVENT_GEN_IMAGE_TOOL,
			membership_level: user?.membershipLevel,
			tool: "vectorize-image",
		});

		try {
			setPreviewImage(null);
			setSubmitting(true);
			const { status, message, resultUrl } = await ofetch("/api/v1/image/tool/vectorize-image", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: {
					image: image?.url,
				},
			});
			handleError(status, message);
			refreshUser();

			// const resultUrl = "https://static.dreampik.art/dev/media/202507/2025072001982826800a7758ae4de4650ec74afd.svg";
			setPreviewImage(resultUrl);
			// if (resultUrl) {
			// 	const base64 = await imageUrlToBase64(resultUrl as string, { noCache: true });
			// 	setPreviewImageBase64(base64);
			// }

			toast.success("Generate success.");
		} catch (error: any) {
			setPreviewImage(null);
			console.error("Failed to generate svg:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error(error.message ?? "Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const [downloadingImage, setDownloadingImage] = useState<boolean>(false);

	return (
		<div className="flex min-h-svh w-full flex-col gap-4 overflow-hidden bg-zinc-900 px-4 py-4 md:flex-row">
			<div className="flex w-full shrink-0 flex-col overflow-y-auto rounded border border-zinc-700 bg-zinc-800 md:h-[90dvh] md:max-w-80 md:min-w-80 xl:max-w-[360px] xl:min-w-[360px]">
				{/* <AppTitleAndBackward title="Vectorize Image" backUrl="/image-tools" /> */}

				<div className="grow space-y-4 p-3 lg:p-4">
					<div className="space-y-[6px]">
						<p className="text-xs text-zinc-300">Image</p>

						{image ? (
							<div className="group relative h-[144px] w-full rounded border border-zinc-700 bg-zinc-900/50 p-0 hover:bg-zinc-900/50">
								<img
									src={image.base64}
									alt="Model"
									className="h-[144px] w-full object-contain"
									onContextMenu={(e) => e.preventDefault()}
									onDragStart={(e) => e.preventDefault()}
								/>
								<div className="absolute inset-0 hidden h-[144px] items-center justify-center bg-black/50 group-hover:flex">
									<button
										onClick={() => {
											setImage(null);
											setPreviewImage(null);
										}}
										className="cursor-pointer rounded-full p-2 text-zinc-300/80 transition-colors hover:text-white"
									>
										<Trash className="size-4" />
									</button>
								</div>
							</div>
						) : (
							<div
								onClick={() => {
									if (!session) {
										setSignInBoxOpen(true);
									}
								}}
							>
								<Dropzone
									multiple={false}
									maxFiles={1}
									noClick={!session}
									onDragEnter={() => {
										if (!session) {
											setSignInBoxOpen(true);
											return;
										}
									}}
									onDrop={(files) => handleLocalFileDrop(files)}
									accept={{
										"image/jpeg": [".jpg", ".jpeg", ".png", ".webp"],
									}}
									onError={console.error}
									className="h-[144px] w-full cursor-pointer border p-0 dark:bg-zinc-900/50 dark:hover:bg-zinc-800"
								>
									<DropzoneEmptyState>
										<>
											{uploadingImage ? (
												<Loader2 className="animate-spin text-zinc-400" />
											) : (
												<div className="text-muted-foreground flex h-10 flex-col items-center gap-2 font-normal hover:text-zinc-400">
													<ImagePlus />
													<p>Click to upload an image</p>
												</div>
											)}
											{/* <p className="w-full text-sm font-normal text-muted-foreground">Or drop an image</p> */}
										</>
									</DropzoneEmptyState>
								</Dropzone>
							</div>
						)}
					</div>
				</div>

				<div className="space-y-0.5 border-t border-zinc-700 p-3 lg:p-4">
					<p className="text-muted-foreground flex flex-row items-center gap-1 text-[11px] font-[350]">
						<CoinsIcon className="size-3" />3 credits per image
					</p>
					<SubmitButton
						variant="secondary"
						className="w-full cursor-pointer bg-indigo-500 hover:bg-indigo-500/80"
						isSubmitting={submitting}
						onClick={handleGenerateImage}
						{...{ disabled: submitting || !image }}
					>
						<Sparkles />
						Vectorize
					</SubmitButton>
				</div>
			</div>

			<div
				className={cn(
					"flex min-h-64 w-full items-start justify-center overflow-y-hidden rounded bg-zinc-800 p-4 md:h-[90dvh]",
					"[&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-zinc-600",
				)}
			>
				<div className="h-full w-full">
					{showDemo ? (
						<div className="mx-auto flex h-full max-w-2xl flex-col justify-center space-y-2">
							<p className="text-sm text-zinc-300">Sample Image</p>
							<div className="mx-auto flex w-full max-w-3xl rounded-md border border-zinc-700 bg-zinc-900/50 p-1">
								<Comparison className="aspect-[3/2] max-w-3xl bg-zinc-900/50">
									<ComparisonItem position="right" className="">
										<div className="relative">
											<img
												src={`${OSS_URL_HOST}mkt/pages/image-tools/image-vectorizer/vectorize-sample-before.webp`}
												alt="Image Upscaler Sample Before"
												className="h-full w-full rounded object-contain"
											/>
											<div
												className="absolute top-2 left-2 rounded-sm bg-zinc-800/80 px-2 py-1 text-xs text-white backdrop-blur-xs after:content-(--content)"
												style={{ "--content": "'Image'" } as React.CSSProperties}
											></div>
										</div>
									</ComparisonItem>
									<ComparisonItem position="left" className="">
										<div className="relative">
											<img
												src={`${OSS_URL_HOST}mkt/pages/image-tools/image-vectorizer/vectorize-sample-after.svg`}
												alt="Image Upscaler Sample After"
												className="h-full w-full rounded object-contain"
											/>
											<div
												className="absolute top-2 right-2 rounded-sm bg-zinc-800/80 px-2 py-1 text-xs text-white backdrop-blur-xs after:content-(--content)"
												style={{ "--content": "'SVG'" } as React.CSSProperties}
											></div>
										</div>
									</ComparisonItem>
									<ComparisonHandle />
								</Comparison>
							</div>
						</div>
					) : (
						<div className="mx-auto flex h-full max-w-3xl flex-col justify-center space-y-4">
							<div
								className={cn(
									"group relative mx-auto flex w-full max-w-3xl justify-center rounded",
									(previewImage || submitting) && "aspect-[3/2] border border-zinc-700 bg-zinc-900/50",
								)}
							>
								{previewImage && (
									<>
										<ReactSVG
											src={previewImage}
											className=""
											wrapper="span"
											beforeInjection={(svg) => {
												svg.removeAttribute("width");
												svg.removeAttribute("height");
												svg.setAttribute("class", "w-full h-full");
												svg.setAttribute("preserveAspectRatio", "xMidYMid meet");
											}}
										/>
										<div
											className="absolute top-2 right-2 rounded-sm bg-zinc-800/80 px-2 py-1 text-xs text-white shadow backdrop-blur-xs after:content-(--content)"
											style={{ "--content": "'SVG'" } as React.CSSProperties}
										></div>
										<div className="absolute right-2 bottom-2 z-10 items-center gap-1">
											<Hint label="Download image" sideOffset={10}>
												<div className="relative">
													<SubmitButton
														isSubmitting={downloadingImage}
														disabled={!previewImage}
														size="icon"
														variant="secondary"
														onClick={async () => {
															try {
																setDownloadingImage(true);
																await downloadImageFromUrl(previewImage);
															} catch (error) {
																console.error("Failed to download image:", error);
															} finally {
																setDownloadingImage(false);
															}
														}}
													>
														<Download />
													</SubmitButton>
												</div>
											</Hint>
										</div>
									</>
								)}
								{submitting && (
									<p className="flex aspect-[3/2] h-full w-full max-w-3xl flex-col items-center justify-center text-center text-zinc-400">
										<LoaderCircle className="size-6 animate-spin" />
										<span className="text-sm tabular-nums">{seconds}s</span>
									</p>
								)}
							</div>
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
