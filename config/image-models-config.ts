import { OSS_URL_HOST } from "@/lib/constants";

export type ModelSeriesType = {
	name: string;
	description?: string;
	url: string;
	logo: string;
	new?: boolean;
};

export enum ImageModelLogo {
	Flux = `${OSS_URL_HOST}icon/model/flux-light.webp`,
	Imagen = `${OSS_URL_HOST}icon/model/google-color.webp`,
	Ideogram = `${OSS_URL_HOST}icon/model/ideogram-light.webp`,
	Recraft = `${OSS_URL_HOST}icon/model/recraft-light.webp`,
	Seedream = `${OSS_URL_HOST}icon/model/bytedance-color.webp`,
	Qwen = `${OSS_URL_HOST}icon/model/qwen-color.webp`,
	HiDream = `${OSS_URL_HOST}icon/model/hidream-color.webp`,
}
export const imageModelSeries: ModelSeriesType[] = [
	{
		name: "<PERSON>lux",
		url: "/models/image/flux",
		logo: ImageModelLogo.Flux,
	},
	{
		name: "Flux Krea",
		url: "/models/image/flux/flux-krea",
		logo: ImageModelLogo.Flux,
		new: true,
	},
	{
		name: "Flux Kontext",
		url: "/models/image/flux/flux-kontext",
		logo: ImageModelLogo.Flux,
	},
	{
		name: "Imagen 4",
		url: "/models/image/imagen",
		logo: ImageModelLogo.Imagen,
	},
	{
		name: "Ideogram",
		url: "/models/image/ideogram",
		logo: ImageModelLogo.Ideogram,
	},
	{
		name: "Recraft",
		url: "/models/image/recraft",
		logo: ImageModelLogo.Recraft,
	},
	{ name: "Seedream 3", url: "/ai-image-generator?model=seedream-3", logo: ImageModelLogo.Seedream },
	{
		name: "HiDream",
		url: "/models/image/hidream",
		logo: ImageModelLogo.HiDream,
	},
];
