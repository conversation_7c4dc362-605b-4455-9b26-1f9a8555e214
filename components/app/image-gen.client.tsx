"use client";

import { useState } from "react";
import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import {
	LucideIcon,
	SparklesIcon,
	Square,
	RectangleHorizontal,
	RectangleVertical,
	LoaderCircle,
	Download,
	Icon,
	ImagePlus,
	Loader2,
	X,
	CoinsIcon,
} from "lucide-react";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { toast } from "sonner";
import { ofetch } from "ofetch";
import { AuthError, Credits402Error, handleError, IgnoreError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { coinsStack } from "@lucide/lab";
import { fileToBase64, imageUrlToBase64 } from "@/lib/file/utils-file";
import { cn } from "@/lib/utils";
import { imageModels, ImageModel, FLUX_1_SCHNELL } from "@/lib/utils-image-model";
import { useSubmitTimer } from "@/hooks/use-submit-timer";
import { uploadFile } from "@/lib/file/upload-file";
import { Dropzone } from "@/components/ui/kibo-ui/dropzone";

const IMAGE_SIZES = [
	{ ratio: "1:1", width: 1024, height: 1024, icon: Square },
	{ ratio: "2:3", width: 683, height: 1024, icon: RectangleVertical },
	{ ratio: "3:2", width: 1024, height: 683, icon: RectangleHorizontal },
	{ ratio: "3:4", width: 768, height: 1024, icon: RectangleVertical },
	{ ratio: "4:3", width: 1024, height: 768, icon: RectangleHorizontal },
	{ ratio: "9:16", width: 576, height: 1024, icon: RectangleVertical },
	{ ratio: "16:9", width: 1024, height: 576, icon: RectangleHorizontal },
] as const;

export default function ImageGenClient() {
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	const [model, setModel] = useState<ImageModel>(FLUX_1_SCHNELL);
	const [prompt, setPrompt] = useState("");
	const [size, setSize] = useState<{
		ratio: string;
		width: number;
		height: number;
		icon: LucideIcon;
	}>(IMAGE_SIZES[0]);
	const numImages = 1;
	const [originImage, setOriginImage] = useState<{
		url: string;
		base64: string;
	} | null>(null);
	const [uploadingImage, setUploadingImage] = useState<boolean>(false);
	const [previewImageBase64s, setPreviewImageBase64s] = useState<string[] | null>(null);
	// const [previewImageBase64s, setPreviewImageBase64s] = useState<string[] | null>(Array(numImages).fill(null));

	const handleLocalFileDrop = async (files: File[]) => {
		if (!session?.user) {
			setSignInBoxOpen(true);
			return;
		}

		setOriginImage(null);

		if (!files || files.length === 0) return;

		try {
			setUploadingImage(true);

			const { file_url } = await uploadFile(files[0]);
			// const file_url = "";
			const base64 = await fileToBase64(files[0]);
			setOriginImage({
				url: file_url,
				base64,
			});
		} catch (error: any) {
			console.error("Failed to upload image:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error(`Upload image failed: ${error.message}`);
		} finally {
			setUploadingImage(false);
		}
	};

	const [submitting, setSubmitting] = useState(false);
	const { seconds } = useSubmitTimer(submitting);
	const handleGenerateImage = async () => {
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		const promtpTrim = prompt.trim();
		if (!promtpTrim) return;

		try {
			setPreviewImageBase64s(Array(numImages).fill(null));
			setSubmitting(true);
			const { status, message, resultUrls } = await ofetch("/api/v1/image/flux", {
				method: "POST",
				body: {
					model: model.id,
					prompt: promtpTrim,
					size: {
						ratio: size.ratio,
						width: size.width,
						height: size.height,
					},
					numImages: numImages,
					image: originImage?.url,
				},
			});
			handleError(status, message);
			refreshUser();
			// const resultUrls = [
			// 	"https://d32s1zkpjdc4b1.cloudfront.net/output/18220959-c7e1-4f70-9a6c-3479a6fe4881-u2_b13edcbc-9723-46c0-95cb-99cadec85930.jpeg",
			// 	"https://v3.fal.media/files/rabbit/voFEj_podAvkaiEcyiHoB_image.png",
			// ];
			if (resultUrls && resultUrls.length > 0) {
				// Create a copy of the current array to modify
				const tempBase64s = Array(resultUrls.length).fill(null);
				// Process each URL and update the state as each completes
				for (let i = 0; i < resultUrls.length; i++) {
					const base64 = await imageUrlToBase64(resultUrls[i] as string);
					tempBase64s[i] = base64;
					setPreviewImageBase64s([...tempBase64s]);
				}
			}

			toast.success("Generate success.");
		} catch (error: any) {
			console.error("Failed to generate image:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}

			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error("Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const [downloading, setDownloading] = useState(false);

	return (
		<div className="flex w-full flex-col items-center gap-6">
			<div className="bg-secondary w-full rounded-xl px-4 pt-4 pb-2 backdrop-blur-3xl">
				<Textarea
					placeholder="Describe an image and click generate..."
					value={prompt}
					maxLength={1500}
					onChange={(e) => setPrompt(e.target.value)}
					className="min-h-[88px] resize-none border-0 px-0 shadow-none focus-visible:ring-0 [&::-webkit-scrollbar]:my-2 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-zinc-300"
				/>

				<div className="mt-2 mb-1 flex flex-wrap items-center justify-between gap-1">
					<div className="flex items-center gap-1">
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button variant="ghost" size="sm" className="justify-between rounded-full border bg-white hover:bg-zinc-50">
									{model?.name}
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent className="w-[160px]">
								<p className="text-muted-foreground px-2 py-1.5 text-sm">Veo models</p>
								{imageModels.map((modelOption, index) => (
									<DropdownMenuItem key={index} className="cursor-pointer items-center" onClick={() => setModel(modelOption)}>
										{modelOption.name}
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
						{originImage ? (
							<div className="group relative aspect-square h-8 w-12 rounded-full border">
								<img
									src={originImage.base64}
									alt="Model"
									className="h-full w-full rounded-full object-cover"
									onContextMenu={(e) => e.preventDefault()}
									onDragStart={(e) => e.preventDefault()}
								/>
								<button
									className="absolute -top-1.5 -right-1.5 z-10 rounded-full"
									onClick={(e) => {
										e.stopPropagation();
										setOriginImage(null);
									}}
								>
									<X className="size-[18px] rounded-full bg-zinc-900 p-1 text-white" strokeWidth={3} />
								</button>
							</div>
						) : (
							<Dropzone
								multiple={false}
								maxFiles={1}
								onDrop={(files) => handleLocalFileDrop(files)}
								accept={{
									"image/jpeg": [".jpg", ".jpeg", ".png", ".webp"],
								}}
								className={cn(buttonVariants({ variant: "ghost", size: "sm" }), "h-8 w-12 rounded-full border bg-white py-0 hover:bg-zinc-50")}
							>
								{uploadingImage ? <Loader2 className="animate-spin text-zinc-800" /> : <ImagePlus />}
							</Dropzone>
						)}
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button variant="ghost" size="sm" className="justify-between rounded-full border bg-white hover:bg-zinc-50">
									<div className="flex items-center gap-2">
										<size.icon className="" />
										{IMAGE_SIZES.find((s) => s.width === size.width && s.height === size.height)?.ratio}
									</div>
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent className="">
								<p className="text-muted-foreground px-2 py-1.5 text-sm">Aspect ratio</p>
								{IMAGE_SIZES.map((sizeOption, index) => (
									<DropdownMenuItem key={index} className="cursor-pointer" onClick={() => setSize(sizeOption)}>
										<div className="flex items-center gap-2">
											<sizeOption.icon className="size-4" />
											{sizeOption.ratio}
										</div>
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
						{/* <DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button
									variant="ghost"
									size="sm"
									className="justify-between bg-zinc-200/50 transition-all duration-200 hover:scale-[102%] hover:bg-zinc-200"
								>
									<div className="flex items-center gap-2">
										{numImages} image{numImages > 1 && "s"}
									</div>
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent className="">
								<p className="px-2 py-1.5 text-sm text-muted-foreground">Number of images</p>
								<Tabs value={numImages.toString()} className="px-2 pb-3 pt-1">
									<TabsList className="h-12 w-full gap-0.5">
										{[1, 2, 3, 4].map((num, index) => (
											<TabsTrigger
												key={index}
												value={num.toString()}
												onClick={() => setNumImages(num)}
												className="h-10 w-10 font-normal hover:bg-zinc-200"
											>
												{num}
											</TabsTrigger>
										))}
									</TabsList>
								</Tabs>
							</DropdownMenuContent>
						</DropdownMenu> */}
					</div>

					<SubmitButton isSubmitting={submitting} onClick={handleGenerateImage} disabled={submitting || prompt.trim().length === 0}>
						<SparklesIcon className="h-4 w-4" />
						<span className="hidden md:block">Generate</span>{" "}
						{/* {session && (
							<p className="ml-1 flex flex-row items-center gap-1 rounded-md bg-orange-500 px-2 py-0.5 text-[11px] font-[350] text-zinc-100">
								<CoinsIcon className="size-3" />
								{model.credits * numImages}
							</p>
						)} */}
					</SubmitButton>
				</div>
			</div>

			{/* {previewImageBase64s && (
				<div
					className={cn(
						"grid w-full gap-4",
						previewImageBase64s.length === 1 && "grid-cols-1",
						previewImageBase64s.length === 2 && "grid-cols-2",
						previewImageBase64s.length > 2 && "grid-cols-2 md:grid-cols-4",
					)}
				>
					{previewImageBase64s.map((base64, index) => (
						<div
							key={index}
							className="group relative mx-auto flex aspect-square h-full w-full max-w-md items-center justify-center rounded-lg bg-zinc-100"
						>
							{base64 ? (
								<img
									src={base64}
									alt={`Generated image ${index + 1}`}
									className="h-full w-full rounded-lg object-contain"
									onContextMenu={(e) => e.preventDefault()}
									onDragStart={(e) => e.preventDefault()}
								/>
							) : (
								submitting && (
									<p className="flex w-full flex-col items-center text-center text-lg text-zinc-500">
										<LoaderCircle className="size-7 animate-spin" />
										<span className="font-mono tabular-nums">{seconds}s</span>
									</p>
								)
							)}
							<div className="absolute bottom-2 right-2 z-10 items-center gap-1">
								{base64 && (
									<Hint label="Download image" sideOffset={10}>
										<div className="relative">
											<SubmitButton
												isSubmitting={downloading}
												disabled={!base64}
												size="icon"
												variant="outline"
												onClick={async () => {
													try {
														setDownloading(true);
														if (userHasPaid) {
															await downloadImageFromBase64(base64);
														} else {
															await downloadImageFromBase64WithWatermark(base64);
														}
													} catch (error) {
														console.error("Failed to download image:", error);
													} finally {
														setDownloading(false);
													}
												}}
											>
												<Download />
											</SubmitButton>
										</div>
									</Hint>
								)}
							</div>
						</div>
					))}
				</div>
			)} */}
		</div>
	);
}
