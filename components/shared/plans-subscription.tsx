"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2, Check, X, Info, RocketIcon } from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { pricingPlans, PricingPlan } from "@/config/pricing";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Subscription } from "@/@types/subscription";
import { AuthError, handleError } from "@/@types/error";
import { useRouter } from "nextjs-toploader/app";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ofetch } from "ofetch";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { canChangePlan } from "@/lib/utils-membership";
import { EVENT_CHECKOUT } from "@/lib/track-events";
import { sendGTMEvent } from "@next/third-parties/google";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function Plans({ isUserPlan = false, userSubscription }: { isUserPlan?: boolean; userSubscription?: Subscription | null }) {
	const router = useRouter();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();

	const [currentTab, setCurrentTab] = useState("yearly");

	// const [isCancelling, setIsCancelling] = useState(false);
	const [isPurchasing, setIsPurchasing] = useState(false);
	const [purchaseProductId, setPurchaseProductId] = useState<string | null>(null);
	const [subscriptionChanged, setSubscriptionChanged] = useState(false);

	const purchase = async (productId: string, planName?: string) => {
		if (userSubscription) return;
		console.log("New subscription checkout");
		sendGTMEvent({ event: EVENT_CHECKOUT, checkout_plan: `${planName}(${currentTab === "monthly" ? "Monthly" : "Yearly"})` });

		try {
			setIsPurchasing(true);
			setPurchaseProductId(productId);

			const { status, message, url } = await ofetch("/api/payment/checkout", {
				method: "POST",
				body: { productId, type: "subscription" },
			});
			if (status === 1001) {
				toast.error("You are already subscribed.");
				setIsPurchasing(false);
				return;
			}
			handleError(status, message);
			if (url) {
				router.push(url);
				// window.open(url, "_self");
			}
		} catch (error) {
			console.error(error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
			} else {
				toast.error("Error creating a checkout.");
			}
			setIsPurchasing(false);
			setPurchaseProductId(null);
		}
	};
	const changePlan = async (productId: string) => {
		if (!userSubscription) return;
		if (userSubscription.productId === productId) return;
		if (isPurchasing) return;
		console.log("Change subscription");

		try {
			setPurchaseProductId(productId);
			setIsPurchasing(true);
			const { status, message } = await ofetch("/api/payment/subscription", {
				method: "POST",
				body: {
					type: "change",
					subscriptionId: userSubscription.subscriptionId,
					productId,
				},
			});
			handleError(status, message);
			setSubscriptionChanged(true);
			toast.success("Your subscription change has been initiated.");
		} catch (error) {
			console.error(error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error("Subscription change failed.");
		} finally {
			setIsPurchasing(false);
			setPurchaseProductId(null);
		}
	};
	// const cancelSubscription = async () => {
	// 	if (!subscription) return;
	// 	if (isCancelling) return;

	// 	try {
	// 		setIsCancelling(true);

	// 		const { status, message } = await ofetch("/api/v1/user/subscription/cancel", {
	// 			method: "POST",
	// 			body: { subscriptionId: subscription.subscriptionId },
	// 		});
	// 		handleError(status, message);
	// 		// const { status, message } = await cancelSubAction(subscription.subscriptionId);
	// 		// handleActionError(status, message);
	// 		router.refresh();
	// 		toast.success("Subscription cancelled.");
	// 		setUserPlanBoxOpen(false);
	// 		setUserSubscription(null);
	// 	} catch (error) {
	// 		console.error(error);
	// 		if (error instanceof AuthError) {
	// 			setSignInBoxOpen(true);
	// 			return;
	// 		}
	// 		toast.error("Error cancelling subscription.");
	// 	} finally {
	// 		setIsCancelling(false);
	// 	}
	// };

	return (
		<div className="relative flex w-full flex-col gap-12">
			<div className="flex items-center justify-center">
				<Tabs value={currentTab} onValueChange={setCurrentTab} className="">
					<TabsList className="h-auto border">
						<TabsTrigger
							value="monthly"
							className="h-8 px-3 font-normal dark:data-[state=active]:border-indigo-500 dark:data-[state=active]:bg-indigo-500/15"
						>
							Monthly
						</TabsTrigger>
						<TabsTrigger
							value="yearly"
							className="h-8 px-3 font-normal dark:data-[state=active]:border-indigo-500 dark:data-[state=active]:bg-indigo-500/15"
						>
							Yearly <span className="rounded bg-zinc-700/50 px-2 py-1 text-xs text-zinc-100">Save 30%+</span>
						</TabsTrigger>
					</TabsList>
				</Tabs>
			</div>

			{subscriptionChanged && (
				<div className="absolute z-20 w-full">
					<Alert className="mx-auto max-w-xl items-center shadow-xl">
						<RocketIcon className="h-4 w-4 text-indigo-500" />
						<AlertTitle className="text-zinc-100">Your subscription change has been initiated.</AlertTitle>
						<AlertDescription className="text-muted-foreground inline text-sm">
							Your order is being processed. It should be completed in 20 seconds to 2 minutes. You can{" "}
							<span className="inline text-indigo-400 italic">refresh the page to check the status</span>.
						</AlertDescription>
					</Alert>
				</div>
			)}

			<div
				className={cn(
					"mx-auto inline-grid w-full max-w-4xl grid-cols-1 justify-center gap-3 text-start md:grid-cols-3 md:gap-0",
					!isUserPlan && "md:grid-cols-3",
				)}
			>
				{pricingPlans.map((pricing: PricingPlan, index: number) => {
					if (isUserPlan && pricing.free) return null;
					return (
						<Card
							key={index}
							className={cn(
								"relative mx-auto w-full max-w-full gap-1 border bg-zinc-900/80 shadow-none hover:bg-zinc-800/80",
								pricing.badge ? "rounded-lg border border-indigo-500/80 bg-[#272842] hover:bg-[#2f2f55]" : "my-4",
								index === 0 && "rounded-lg rounded-l-lg md:rounded-r-none",
								index === pricingPlans.length - 1 && "rounded-lg rounded-r-lg md:rounded-l-none",
							)}
						>
							<div className={cn(pricing.badge && "h-4")}></div>
							<CardHeader>
								<CardTitle className="flex flex-row items-center justify-between gap-1 text-xl font-medium">
									{pricing.title}
									{pricing.badge && (
										<Badge
											variant="secondary"
											className="border-indigo-500 bg-indigo-500/60 bg-linear-to-r font-normal hover:bg-indigo-500/60"
										>
											{pricing.badge}
										</Badge>
									)}
								</CardTitle>
							</CardHeader>

							<CardContent className="flex flex-col gap-4">
								<div className="flex flex-wrap items-end">
									<span className="text-lg font-medium">
										{pricing.currency.symbol}
										{pricing.free || currentTab === "monthly" ? pricing.price.monthly : pricing.price.monthForYearly}
									</span>
									<div className="mb-1 ml-1 leading-none">
										<p className="text-muted-foreground font- text-sm">{!pricing.free && pricing.duration}</p>
									</div>
								</div>

								{pricing.free ? (
									<Button variant="default" disabled className="w-full max-w-xs">
										Get started
									</Button>
								) : !userSubscription ? (
									<Button
										{...{
											variant: pricing.badge ? "secondary" : "default",
											disabled:
												isPurchasing &&
												purchaseProductId === (currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly),
										}}
										className={cn("w-full", pricing.badge && "bg-indigo-500 hover:bg-indigo-500/80")}
										onClick={() => {
											purchase(currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly, pricing.title);
										}}
									>
										<p className="flex flex-row items-center space-x-1">
											<span>Subscribe now</span>
											{isPurchasing &&
												purchaseProductId === (currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly) && (
													<Loader2 className="h-4 w-4 animate-spin" />
												)}
										</p>
									</Button>
								) : userSubscription.productId === (currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly) ? (
									<Button
										{...{
											variant: pricing.badge ? "secondary" : "default",
										}}
										disabled
										className={cn("w-full", pricing.badge && "bg-indigo-500 hover:bg-indigo-500/80")}
									>
										<p className="flex flex-row items-center space-x-1">Current plan</p>
									</Button>
								) : (
									<AlertDialog>
										<AlertDialogTrigger asChild>
											<Button
												{...{
													variant: pricing.badge ? "secondary" : "default",
													disabled:
														(isPurchasing &&
															purchaseProductId ===
																(currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly)) ||
														!canChangePlan(
															userSubscription.productId,
															currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly,
														),
												}}
												className={cn("w-full", pricing.badge && "bg-indigo-500 hover:bg-indigo-500/80")}
											>
												<p className="flex flex-row items-center space-x-1">
													<span>Change</span>
													{isPurchasing &&
														purchaseProductId ===
															(currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly) && (
															<Loader2 className="h-4 w-4 animate-spin" />
														)}
												</p>
											</Button>
										</AlertDialogTrigger>
										<AlertDialogContent>
											<AlertDialogHeader>
												<AlertDialogTitle>Are you sure you want to change your subscription plan?</AlertDialogTitle>
												<AlertDialogDescription>
													Your plan will change right away. We’ll adjust your bill to match your new plan.
												</AlertDialogDescription>
											</AlertDialogHeader>
											<AlertDialogFooter>
												<AlertDialogCancel>Cancel</AlertDialogCancel>
												<AlertDialogAction
													onClick={() => {
														changePlan(currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly);
													}}
												>
													Change
												</AlertDialogAction>
											</AlertDialogFooter>
										</AlertDialogContent>
									</AlertDialog>
								)}

								<div className="flex flex-col gap-4">
									<div className="flex flex-col gap-3 text-sm text-zinc-200">
										{pricing.features?.map((feature: any, index: any) => (
											<p key={index} className="flex items-start gap-3">
												<Check className="mt-0.5 h-4 w-4 shrink-0 text-indigo-400" />
												<span className="inline-flex items-center gap-1 font-[380]">
													{feature.description}
													{feature.tips && (
														<TooltipProvider delayDuration={100}>
															<Tooltip>
																<TooltipTrigger>
																	<Info className="text-muted-foreground h-4 w-4" />
																</TooltipTrigger>
																<TooltipContent className="bg-black">
																	<p style={{ whiteSpace: "pre-wrap" }}>{feature.tips}</p>
																</TooltipContent>
															</Tooltip>
														</TooltipProvider>
													)}
												</span>
											</p>
										))}

										{pricing.unFeatures?.map((feature: any, index: any) => (
											<p key={index} className="flex items-start gap-3">
												<X className="mt-0.5 h-4 w-4 shrink-0 text-red-700" />
												<span className="">{feature}</span>
											</p>
										))}
									</div>
								</div>
							</CardContent>

							<div className={cn(pricing.badge && "h-4")}></div>
						</Card>
					);
				})}
			</div>
		</div>
	);
}
