import { falGenImages } from "./fal-config.server";

export async function genHidreamE1FromFal(model: string, prompt: string, numImages: number, image?: string): Promise<string[]> {
	const falAIEndPoint = "fal-ai/hidream-e1-1";
	const payload: any = {
		edit_instruction: prompt,
		num_images: numImages,
		image_url: image,
	};
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai hidream.e1.1 payload: ", payload);
		console.log("fal.ai hidream.e1.1 falRequestModelName:", falAIEndPoint);
	}

	const resultUrls: string[] = await falGenImages(falAIEndPoint, payload, 5000, 2000);

	return resultUrls;
}

export async function genHidreamI1FromFal(
	model: string,
	prompt: string,
	numImages: number,
	aspectRatio: {
		ratio: string;
		width: number;
		height: number;
	},
): Promise<string[]> {
	const falAIEndPoint = "fal-ai/hidream-i1-dev";
	const payload: any = {
		prompt: prompt,
		num_images: numImages,
		image_size: {
			width: aspectRatio.width,
			height: aspectRatio.height,
		},
	};
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai hidream.i1 dev payload: ", payload);
		console.log("fal.ai hidream.i1 dev falRequestModelName:", falAIEndPoint);
	}

	const resultUrls: string[] = await falGenImages(falAIEndPoint, payload, 5000, 2000);

	return resultUrls;
}
