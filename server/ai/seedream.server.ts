import { falGenImages } from "./fal-config.server";

export async function genSeedream3FromFal(model: string, prompt: string, numImages: number, aspectRatio: string): Promise<string[]> {
	const falAIEndPoint = "fal-ai/bytedance/seedream/v3/text-to-image";
	const payload: any = {
		prompt: prompt,
		num_images: numImages,
		aspect_ratio: aspectRatio,
	};
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai Seedream 3 payload: ", payload);
	}

	const resultUrls: string[] = await falGenImages(falAIEndPoint, payload, 5000, 2000);

	return resultUrls;
}
