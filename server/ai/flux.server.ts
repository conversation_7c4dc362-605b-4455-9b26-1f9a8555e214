import { FLUX_1_1_PRO, FLUX_1_1_PRO_ULTRA, FLUX_1_KONTEXT_MAX, FLUX_1_KONTEXT_PRO } from "@/lib/utils-image-model";
import { ofetch } from "ofetch";
import { falGenImages } from "./fal-config.server";

export async function genFluxSchnellFromFal(
	model: string,
	prompt: string,
	numImages: number,
	aspectRatio: {
		ratio: string;
		width: number;
		height: number;
	},
): Promise<string[]> {
	let falAIEndPoint = "fal-ai/flux/schnell";
	let payload: any = {
		prompt: prompt,
		num_images: numImages,
		image_size: {
			width: aspectRatio.width,
			height: aspectRatio.height,
		},
	};
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai flux.1 schnell payload: ", payload);
		console.log("fal.ai flux.1 schnell falRequestModelName:", falAIEndPoint);
	}

	const resultUrls: string[] = await falGenImages(falAIEndPoint, payload, 2000, 1000);

	return resultUrls;
}

export async function genFluxDevFromFal(
	model: string,
	prompt: string,
	numImages: number,
	aspectRatio: {
		ratio: string;
		width: number;
		height: number;
	},
	image?: string,
): Promise<string[]> {
	let falAIEndPoint = "fal-ai/flux/dev";
	let payload: any = {
		prompt: prompt,
		num_images: numImages,
	};
	if (image) {
		falAIEndPoint = "fal-ai/flux/dev/image-to-image";
		payload.image_url = image;
	} else {
		payload.image_size = {
			width: aspectRatio.width,
			height: aspectRatio.height,
		};
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai flux.1 dev payload: ", payload);
		console.log("fal.ai flux.1 dev falRequestModelName:", falAIEndPoint);
	}

	const resultUrls: string[] = await falGenImages(falAIEndPoint, payload, 3000, 1000);

	return resultUrls;
}

export async function genFluxKreaFromFal(
	model: string,
	prompt: string,
	numImages: number,
	aspectRatio: {
		ratio: string;
		width: number;
		height: number;
	},
	image?: string,
): Promise<string[]> {
	let falAIEndPoint = "fal-ai/flux/krea";
	let payload: any = {
		prompt: prompt,
		num_images: numImages,
	};
	if (image) {
		payload.image_url = image;
	} else {
		payload.image_size = {
			width: aspectRatio.width,
			height: aspectRatio.height,
		};
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai flux.1 krea dev payload: ", payload);
		console.log("fal.ai flux.1 krea dev falRequestModelName:", falAIEndPoint);
	}

	const resultUrls: string[] = await falGenImages(falAIEndPoint, payload, 2500, 1000);

	return resultUrls;
}

export async function genFluxProFromFal(
	model: string,
	prompt: string,
	numImages: number,
	aspectRatio: {
		ratio: string;
		width: number;
		height: number;
	},
	image?: string,
): Promise<string[]> {
	let falAIEndPoint = "fal-ai/flux-pro/v1.1";
	let payload: any = {
		prompt: prompt,
		num_images: numImages,
	};
	if (model === FLUX_1_1_PRO.model) {
		payload.image_size = {
			width: aspectRatio.width,
			height: aspectRatio.height,
		};
	} else {
		payload.aspect_ratio = aspectRatio.ratio;
		falAIEndPoint = "fal-ai/flux-pro/v1.1-ultra";
	}
	if (image) {
		payload.image_url = image;
		falAIEndPoint += "/redux";
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai flux1.1 pro payload: ", payload);
		console.log("fal.ai flux1.1 pro falRequestModelName:", falAIEndPoint);
	}

	let waitSubmit = 5000;
	if (model === FLUX_1_1_PRO_ULTRA.model) {
		waitSubmit = 10000;
	}
	const resultUrls: string[] = await falGenImages(falAIEndPoint, payload, waitSubmit, 2000);

	return resultUrls;
}

export async function genFluxProKontextFromFal(
	model: string,
	prompt: string,
	numImages: number,
	aspectRatio?: {
		ratio: string;
		width: number;
		height: number;
	},
	image?: string,
): Promise<string[]> {
	let falAIEndPoint = "fal-ai/flux-pro/kontext";
	let payload: any = {
		prompt: prompt,
		num_images: numImages,
	};
	if (model === FLUX_1_KONTEXT_MAX.model) {
		falAIEndPoint = "fal-ai/flux-pro/kontext/max";
	}
	if (image) {
		payload.image_url = image;
	} else {
		if (aspectRatio) {
			payload.aspect_ratio = aspectRatio.ratio;
		}
		falAIEndPoint += "/text-to-image";
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai flux1 kontext payload: ", payload);
		console.log("fal.ai flux1 kontext endpoint:", falAIEndPoint);
	}

	let waitSubmit = 8000;
	if (model === FLUX_1_KONTEXT_PRO.model) {
		waitSubmit = 4500;
	}
	const resultUrls: string[] = await falGenImages(falAIEndPoint, payload, waitSubmit, 2000);

	return resultUrls;
}

export async function genFluxKontextDevFromFal(prompt: string, numImages: number, image: string): Promise<string[]> {
	let falAIEndPoint = "fal-ai/flux-kontext/dev";
	let payload: any = {
		prompt: prompt,
		num_images: numImages,
		image_url: image,
		resolution_mode: "auto",
	};

	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai flux1 kontext dev payload: ", payload);
	}

	const resultUrls: string[] = await falGenImages(falAIEndPoint, payload, 4000, 1500);

	return resultUrls;
}

export async function genFluxFromWavespeed(
	model: string,
	prompt: string,
	numImages: number,
	size: {
		width: number;
		height: number;
	},
	image?: string,
): Promise<string[]> {
	let payload: any = {
		num_images: numImages,
		prompt: prompt,
		size: `${size.width}*${size.height}`,
	};
	if (image) {
		payload.image = image;
	}

	if (process.env.NODE_ENV === "development") {
		console.log("wavespeed payload: ", payload);
	}
	// throw new Error("wavespeed test error");

	const { code, message, data } = await ofetch(`https://api.wavespeed.ai/api/v2/wavespeed-ai/${model}`, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			Authorization: `Bearer ${process.env.WAVESPEED_API_KEY}`,
		},
		body: payload,
	});
	if (code !== 200) {
		throw new Error(`Failed to generate image. ${message}`);
	}
	// console.log("data: ", data);
	const requestId = data.id;
	const getUrl = data.urls.get;

	let resultUrls: string[] | null = null;
	await new Promise((resolve) => setTimeout(resolve, 4000)); // wait for 4 seconds
	while (true) {
		const {
			code: resultCode,
			message: resultMessage,
			data: resutlData,
		} = await ofetch(`https://api.wavespeed.ai/api/v2/predictions/${requestId}/result`, {
			headers: {
				Authorization: `Bearer ${process.env.WAVESPEED_API_KEY}`,
			},
		});
		if (resultCode !== 200) {
			console.error(`Error: ${resultCode}, ${resultMessage}`);
			throw new Error(`Failed to get image result. ${resultMessage}`);
		}
		const { status, outputs, error } = resutlData;
		// status: processing, completed, failed
		if (status === "completed") {
			resultUrls = outputs;
			// console.log("Task completed. URL:", resultUrls);
			break;
		} else if (status === "failed") {
			console.error("Task failed:", error);
			throw new Error(`Failed to generate image. Result: ${error}`);
		} else {
			// console.log("Task still processing. Status:", status);
		}

		await new Promise((resolve) => setTimeout(resolve, 1500)); // wait for 1.5 seconds
	}
	if (!resultUrls) {
		throw new Error("Failed to generate image.");
	}
	return resultUrls;
}
